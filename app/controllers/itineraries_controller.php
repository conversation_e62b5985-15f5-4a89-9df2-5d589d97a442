<?php
use ContentHub\CcapiService;
use ContentHub\SearchToursRequest;

class ItinerariesController extends AppController
{

    public $name = 'Itineraries';
    public $components = array('Section', 'Navigation');

    public function webadmin_add($parentId = null)
    {
        $searchTours = function () {
            return $this->searchTours();
        };

        $itineraryCodes = $this->cacher('itineraryCodes', $searchTours);

        $itineraryCodes = array_reduce($itineraryCodes, function ($result, $tour) {
            $result[$tour->getTourCode() . '_' . $tour->getSellingCompanyCode()] = $tour->getTourName();
            return $result;
        }, array());

        asort($itineraryCodes);

        $this->set(array(
            'itineraryCodes' => $itineraryCodes,
        ));

        parent::webadmin_add($parentId);
    }

    public function index()
    {
        $this->paginate['Itinerary'] = array(
            'limit' => 36,
            'recursive' => 0,
            'order' => 'name',
            'contain' => array('Image'),
        );

        if ($this->params['section']) {
            $sectionModel = $this->Section->sectionSettings[$this->params['section']]['model'];

            $sectionForeignKey = Inflector::underscore($sectionModel).'_id';

            $joinTable = $this->{$this->modelClass}->hasAndBelongsToMany[$sectionModel]['joinTable'];

            $with = Inflector::classify($joinTable);

            $this->Itinerary->bindModel(array(
                'hasOne' => array(
                    $with
                ),
            ), false);

            $this->paginate['Itinerary']['contain'][] = $with;

            $this->paginate['Itinerary']['order'] = $with.'.order';

            $this->paginate['Itinerary']['conditions'] = array(
                $with.'.'.$sectionForeignKey => $this->sectionId
            );
        }

        $itineraries = $this->paginate('Itinerary');

        $this->_canonicalUrlForPaginated();

        $this->set(compact('itineraries'));

        $this->_setMeta('Sample Itineraries');

    }

    public function view()
    {
        $itinerary = $this->_findBySlug($this->params['itinerary_slug']);

        if (empty($itinerary)) {
            $this->cakeError('error404');
        }

        $this->_canonicalUrl(array(
            'itinerary_slug' => $this->params['itinerary_slug'],
            'section'        => 'destinations'
        ));

        $this->set(compact('itinerary'));

        $this->_setMeta($itinerary['Itinerary']);

        $mapData = $this->_getMapData($itinerary['Itinerary']['id']);



        $this->set(array_merge(
            compact('mapData'),
            array(
                'hideTheImage' => true,
                'showMap' => true,
            )
        ));
    }

    /**
    * Return the itinerary from the url slug
    *
    * @return array
    **/
    protected function _findBySlug($slug)
    {
        $findBySlug = function() use ($slug) {

            return $this->Itinerary->getBySlug($slug);

        };

        return $this->cacher(implode('_', array(
            'itinerary', $slug
        )), $findBySlug);
    }

    /**
    * Return map data for the given itinerary id
    *
    * @return array
    **/
    protected function _getMapData($id)
    {
        $getMapData = function() use ($id) {

            return $this->Itinerary->getMapData($id);

        };

        return $this->cacher(implode('_', array(
            'itinerary', $id, 'map_data'
        )), $getMapData);
    }

    public function yahooGeoProxy()
    {
        $this->layout = false;

        App::import('Core', 'HttpSocket');
        $Http = new HttpSocket();

        $query = urlencode(substr($this->params['url']['url'], 12, -1));

        $response = $Http->get('http://where.yahooapis.com/v1/places.q(' . $query . ')', $this->params['url']);

        $this->RequestHandler->respondAs('javascript');

        $this->set(compact('response'));
    }

    /**
    * Get the list of of itineraries from TravCorp
    *
    * @return array
    **/
    protected function searchTours($page = 1)
    {
        $perPage = 100;

        $client = new CcapiService(
            array('attachment_type' => 2),
            Configure::read('TravCorp.wsdl')
        );

        $request = new SearchToursRequest(
            Configure::read('TravCorp.key'),
            array(Configure::read('TravCorp.selling_company'), 'IVUKLS'),
            (($page * $perPage) - $perPage) + 1,
            $perPage
        );

        $request->setContinentCodes('NAME');
        $request->setOrderBy(2);
        $request->setOrderDirection('ASC');

        $tours = array();

        try {
            $itineraryCodes = $client->SearchTours($request);
        } catch (Exception $e) {
            return $tours;
        }

        $tours = array_merge($tours, $itineraryCodes->getSearchResults());

        if ($itineraryCodes->getTotalRecords() > 100 && $itineraryCodes->getNumberOfRecords() == 100) {
            $tours = array_merge($tours, $this->searchTours($page + 1));
        }

        return $tours;
    }
}
